declare module "@cashfreepayments/cashfree-js" {
  interface CashfreeInstance {
    checkout(options: CheckoutOptions): Promise<CheckoutResult>;
  }

  export type CheckoutOptions = {
    paymentSessionId: string;
    redirectTarget?: "_self" | "_blank" | "_parent" | "_top" | "_modal";
  };

  export type CheckoutResult = {
    error?: {
      type: string;
      message: string;
    };
    redirect?: boolean;
  };

  type CashfreeInstance = {
    checkout(options: {
      paymentSessionId: string;
      redirectTarget?: "_self" | "_blank" | "_parent" | "_top";
    }): Promise<CheckoutResult>;
  };

  // Load function that returns the Cashfree instance
  export function load(config: {
    mode: "sandbox" | "production";
  }): Promise<CashfreeInstance>;
}
