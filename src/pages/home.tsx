import Head from "@/components/ui/head";
import BackedByBest from "./home/<USER>/backed-by-best/backed-by-best";
import Footer from "./landing-page/components/footer";
import HighReturns from "./home/<USER>/high-returns-statement";
import KnowMore from "./home/<USER>/know-more";
import { useSuspenseQuery } from "@tanstack/react-query";
import { personalizationPageQuery } from "@/queries/bonds";
import { buildPersonalizationWidget } from "@/components/personalization/builder";
import Nudges from "@/components/functional/nudges/nudges";
import SecondaryHeader from "@/components/layouts/secondary-header";
import FaqSupport from "./home/<USER>/faq-support";
import InvestmentsKycSummary from "./home/<USER>/kyc-investment";

function WebAppHomePage() {
  const { data: pageResponse } = useSuspenseQuery(
    personalizationPageQuery("bonds-home")
  );
  return (
    <>
      {buildPersonalizationWidget(pageResponse.root)}
      <Nudges pageUri="bonds-home" />
    </>
  );
}

export default function Home() {
  const { data: pageResponse } = useSuspenseQuery(
    personalizationPageQuery("web-bonds-home")
  );

  return (
    <>
      <Head title="Home" />
      <div className="hidden md:block">
        <SecondaryHeader />
        <div className="mx-auto max-w-[1250px] space-y-7 px-12 py-12 lg:px-18">
          <div className="space-y-[18px]">
            <h1 className="text-[48px] leading-[56px] tracking-[-2px]">
              Invest in bonds and earn
              <br />
              <span className="relative font-serif font-medium text-[#B67E25] italic">
                9 <span className="text-[40px]">to</span> 12%
              </span>
              &nbsp;<span>returns</span>
            </h1>
          </div>
          <div className="space-y-22">
            <div className="flex gap-6">
              <div className="flex-1 space-y-22">
                {buildPersonalizationWidget(pageResponse.root)}
                <HighReturns />
                <KnowMore />
                <BackedByBest />
              </div>
              <div className="w-[377px]">
                <div className="sticky top-18">
                  <InvestmentsKycSummary />
                </div>
              </div>
            </div>
            <FaqSupport />
          </div>
        </div>
        <Footer />
      </div>
      <div className="md:hidden">
        <WebAppHomePage />
      </div>
    </>
  );
}
