import SecondaryHeader from "@/components/layouts/secondary-header";
import { Outlet, useSearchParams } from "react-router";

export default function CompliancePage() {
  const [searchParams] = useSearchParams();
  return (
    <>
      {!searchParams.has("is_app") && <SecondaryHeader />}
      <div className="mx-auto flex max-w-[900px] flex-col justify-center px-5 py-5">
        <Outlet />
      </div>
    </>
  );
}
