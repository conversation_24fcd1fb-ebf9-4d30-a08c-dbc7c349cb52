import { trackEvent } from "@/utils/analytics";
import { load } from "@cashfreepayments/cashfree-js";
import * as native from "@/utils/native-integration";

export async function startCheckout(
  paymentSessionId: string,
  orderId: string
): Promise<FlutterCashfreeResponse> {
  trackEvent("cashfree_initiated", { paymentSessionId, orderId });
  if (native.checkout.isSupported()) {
    try {
      const data = await native.checkout.start(paymentSessionId, orderId);
      trackEvent("bonds_payment_success", data);
      return data;
    } catch (error) {
      trackEvent("bonds_payment_failed", { paymentSessionId, orderId, error });
      throw new Error("Payment failed");
    }
  }

  const cashfree = await load({ mode: import.meta.env.VITE_CASHFREE_ENV });
  const checkoutOptions = {
    paymentSessionId: paymentSessionId,
    redirectTarget: "_modal" as const,
  };
  const result = await cashfree.checkout(checkoutOptions);
  if (result.redirect) {
    trackEvent("bonds_payment_success", result);
    return { orderId };
  }
  trackEvent("bonds_payment_failed", result);
  throw result;
}
