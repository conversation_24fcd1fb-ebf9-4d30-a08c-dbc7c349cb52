import { useMachine } from "@xstate/react";
import { filterSortMachine, type FilterOptionValueWithLabel } from "./machine";
import BondsListing from "./components/bonds-listing";
import {
  SortType,
  type FilterOptionValue,
} from "@/clients/gen/broking/FilterSearch_pb";
import QuickFilter from "./components/sort-filters/quick-filter";
import AppliedFilters from "./components/sort-filters/applied-filter";
import ErrorPage from "@/components/functional/error-page";
import FloatingFooterContent from "@/components/ui/floating-footer-content";
import YtmModal from "./modals/ytm";
import SortButton from "./modals/sort";
import FilterButton from "./modals/filter";

const FilterSortPage = () => {
  const [state, send] = useMachine(filterSortMachine);
  const {
    filterConfig,
    currentPage,
    totalPages,
    isLoadingResults,
    error,
    isLoadingConfig,
  } = state.context;
  const quickFilter = filterConfig?.filter?.items.filter(
    (item) => item.isQuickFilter
  );
  const hasConfigError = state.matches("configError");
  const hasResultsError = state.matches("resultsError");
  const hasError = hasConfigError || hasResultsError;
  const isEmptyState =
    state.context.emptyState && state.context.emptyState.alternateItems.length;

  const DEFAULT_SORT = SortType.YTM_HIGH_TO_LOW;
  const isSortChanged = state.context.currentSort !== DEFAULT_SORT;

  document.getElementById("url-bar")?.classList.add("hidden");

  const handlePageChange = (page: number) => {
    send({ type: "SET_PAGE", page });
  };

  const handleSort = (sortType: SortType) => {
    send({ type: "UPDATE_SORT", sortType });
  };

  const handleClearFilters = () => {
    send({ type: "CLEAR_FILTERS" });
  };

  const handleFilterRemoval = (label: string) => {
    const currentFilters = state.context.appliedFilters;

    const updatedFilters = currentFilters
      .map((group) => ({
        ...group,
        filters: group.filters.filter((filter) => filter.label !== label),
      }))
      .filter((group) => group.filters.length > 0);
    send({
      type: "APPLY_FILTERS",
      filters: updatedFilters,
    });
  };

  const handleRetryConfig = () => {
    send({ type: "RETRY_LOAD_CONFIG" });
  };

  const handleRetryResults = () => {
    send({ type: "RETRY_LOAD_RESULTS" });
  };

  const handleFilterChange = (
    filter: {
      key: string;
      filters: FilterOptionValue;
      label: string;
    },
    isSelected: boolean
  ) => {
    // Don't update appliedFilters in state machine for live_bonds filter
    if (filter.key === "live_bonds") {
      return;
    }

    const currentFilters = state.context.appliedFilters;
    (filter.filters as FilterOptionValueWithLabel).label = filter.label;
    let updatedFilters;

    if (isSelected) {
      const existingFilterIndex = currentFilters.findIndex(
        (f) => f.key === filter.key
      );

      if (existingFilterIndex >= 0) {
        updatedFilters = currentFilters.map((f, index) =>
          index === existingFilterIndex
            ? {
                ...f,
                filters: [...f.filters, filter.filters],
              }
            : f
        );
      } else {
        updatedFilters = [
          ...currentFilters,
          {
            key: filter.key,
            filters: [filter.filters],
          },
        ];
      }
    } else {
      updatedFilters = currentFilters
        .map((f) => {
          if (f.key === filter.key) {
            const filteredOptions = f.filters.filter((option) => {
              return JSON.stringify(option) !== JSON.stringify(filter.filters);
            });
            return {
              ...f,
              filters: filteredOptions,
            };
          }
          return f;
        })
        .filter((f) => f.filters.length > 0);
    }

    send({
      type: "APPLY_FILTERS",
      filters: updatedFilters,
    });
  };

  if (hasError) {
    return (
      <ErrorPage
        error={error}
        title={error ? undefined : "Failed to load bonds"}
        description="Please try again. If the issue persists, contact our support team."
        onRetry={hasConfigError ? handleRetryConfig : handleRetryResults}
      />
    );
  }

  return (
    <>
      <div className="flex flex-col bg-white pt-0 pr-5 pl-5">
        <div className="flex flex-col gap-3 pt-5">
          <QuickFilter
            quickFilter={quickFilter}
            handleFilterChange={handleFilterChange}
            appliedFilters={state.context.appliedFilters}
            isLoading={isLoadingConfig}
          />
          {state.context.appliedFilters.length > 0 && (
            <AppliedFilters
              appliedFilters={state.context.appliedFilters}
              handleClearFilters={handleClearFilters}
              handleFilterRemoval={handleFilterRemoval}
            />
          )}
        </div>
      </div>
      <div className="sticky top-0 z-1 flex justify-between bg-white p-5">
        <p className="text-body2 font-medium">
          {!isEmptyState
            ? state.context.totalResults
            : state.context.emptyState?.alternateItems.length}{" "}
          results
        </p>
        <div className="text-body2 flex gap-1 font-medium underline [text-decoration-color:#BBB] [text-decoration-style:dotted] [text-decoration-thickness:10.5%] [text-underline-offset:40%] [text-decoration-skip-ink:none] [text-underline-position:from-font]">
          Yield to maturity
          <YtmModal />
        </div>
      </div>

      <BondsListing
        listData={state.context.bonds}
        onPageChange={handlePageChange}
        currentPage={currentPage}
        totalPages={totalPages}
        isLoading={isLoadingResults}
        isFilterLoading={isLoadingResults && state.context.currentPage === 0}
        suggestedFilters={state.context.suggestedFilters}
        handleFilterChange={handleFilterChange}
        appliedFilters={state.context.appliedFilters}
        emptyState={state.context.emptyState}
      />
      <FloatingFooterContent>
        <div className="pb-safe flex gap-3 bg-white">
          <SortButton
            filterConfig={filterConfig}
            handleSort={handleSort}
            currentSort={state.context.currentSort}
            isSortChanged={isSortChanged}
          />
          <FilterButton
            currentPage={currentPage}
            totalResults={state.context.totalResults}
            filterConfig={filterConfig}
            handleFilterChange={handleFilterChange}
            appliedFilters={state.context.appliedFilters}
            handleClearFilters={handleClearFilters}
            isLoadingResults={isLoadingResults}
          />
        </div>
      </FloatingFooterContent>
    </>
  );
};

export default FilterSortPage;
