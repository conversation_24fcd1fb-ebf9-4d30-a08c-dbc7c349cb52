import { useEffect, useRef, useState } from "react";
import clsx from "clsx";
import type { TagProps } from "./types";
import classes from "./tag.module.css";

// Shimmer SVG component
const ShimmerSVG = ({ translateWidth }: { translateWidth: string }) => (
  <svg
    width="46"
    height="48"
    viewBox="0 0 46 48"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={clsx("absolute h-5 w-4", classes.shimmer)}
    style={
      {
        color: "var(--shimmer-color)",
        "--translate-width": translateWidth,
      } as React.CSSProperties
    }
  >
    <path
      opacity="0.13"
      d="M22.6667 0H46L34.3333 48H11L22.6667 0Z"
      fill="currentColor"
    />
    <path opacity="0.13" d="M11.4 0H19L7.6 48H0L11.4 0Z" fill="currentColor" />
  </svg>
);

export default function Tag({
  color = "#916CFF",
  backgroundColor = "#F8F5FF",
  shimmerColor = "#916CFF",
  borderColor = "#916CFF33",
  hasShimmer = false,
  children,
  leading,
  trailing,
  className,
  style,
  ...rest
}: TagProps) {
  const tagRef = useRef<HTMLDivElement>(null);
  const [translateWidth, setTranslateWidth] = useState("0px");

  useEffect(() => {
    if (!tagRef.current || !hasShimmer) return;

    const element = tagRef.current;

    // Set CSS custom properties
    element.style.setProperty("--tag-color", color);
    element.style.setProperty("--shimmer-color", shimmerColor);

    // Set up ResizeObserver for shimmer animation
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const width = entry.target.clientWidth + 12;
        setTranslateWidth(`${width}px`);
        element.style.setProperty("--translate-width", `${width}px`);
      }
    });

    resizeObserver.observe(element);

    return () => {
      resizeObserver.disconnect();
    };
  }, [color, shimmerColor, hasShimmer]);

  const combinedStyle = {
    "--tag-color": color,
    "--shimmer-color": shimmerColor,
    "--translate-width": translateWidth,
    ...style,
  } as React.CSSProperties;

  return (
    <>
      <div
        className={clsx("inline-flex", className)}
        style={combinedStyle}
        {...rest}
      >
        <div
          className={clsx(
            "text-caption relative overflow-hidden rounded-r-xs rounded-bl-xs leading-none font-medium",
            {
              "border-[0.3px]": borderColor,
            }
          )}
          style={{
            color,
            backgroundColor,
            borderColor,
          }}
        >
          <div ref={tagRef} className="relative h-full">
            {hasShimmer && <ShimmerSVG translateWidth={translateWidth} />}
            <div className="flex h-full items-center gap-1 px-1.5 py-1 uppercase">
              {leading}
              {children}
              {trailing}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
