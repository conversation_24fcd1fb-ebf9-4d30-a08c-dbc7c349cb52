import { useRadioGroupContext } from "./context";
import clsx from "clsx";
import type { RadioItemProps } from "./types";

export default function RadioItem({
  value,
  disabled = false,
  children,
  className,
  ...rest
}: RadioItemProps) {
  const api = useRadioGroupContext();

  if (!api) {
    throw new Error("RadioItem must be used within a RadioGroup");
  }

  const isChecked = api.value === value;
  const isDisabled = disabled;

  const classes = clsx(
    "flex items-center gap-2 cursor-pointer text-black-50",
    "text-heading4",
    // State-dependent styles
    {
      "text-black-80": isChecked,
      "opacity-50 cursor-not-allowed": isDisabled,
    },
    className
  );

  return (
    <>
      <input {...api.getItemHiddenInputProps({ value, disabled })} />
      <label
        {...api.getItemProps({ value, disabled })}
        {...rest}
        className={classes}
      >
        <div
          className={clsx(
            // Base indicator styles
            "flex h-4 w-4 items-center justify-center rounded-full border bg-white transition-all duration-200",
            // Border styles based on state
            {
              "border-black-20": !isChecked && !isDisabled,
              "border-black": isChecked,
              "hover:border-black-40": !isDisabled && !isChecked,
            }
          )}
        >
          {isChecked && <div className="h-2.5 w-2.5 rounded-full bg-black" />}
        </div>
        <div className="flex-1">{children}</div>
      </label>
    </>
  );
}
