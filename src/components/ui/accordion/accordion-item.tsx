import clsx from "clsx";
import type { AccordionItemProps } from "./types";
import { useAccordionContext } from "./context";
import { useId } from "react";

export default function AccordionItem({
  label,
  id,
  children,
  isDarkMode = false,
  className,
  ...rest
}: AccordionItemProps) {
  const api = useAccordionContext();
  const defaultId = useId();

  return (
    <div
      className={clsx("group flex flex-col gap-2 py-3", className)}
      {...rest}
      {...api?.getItemProps({ value: id || defaultId })}
    >
      <button
        className={clsx(
          "flex cursor-pointer items-center justify-between gap-5 text-left",
          { "text-white": isDarkMode }
        )}
        {...api?.getItemTriggerProps({ value: id || defaultId })}
      >
        <span className="text-body1 flex-1">{label}</span>
        {children ? (
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className={clsx(
              "block size-4 flex-shrink-0 rotate-90 transition-transform duration-300 group-data-[state=open]:rotate-270",
              isDarkMode ? "text-white" : "text-black-80"
            )}
          >
            <path
              d="M8 5L15 12L8 19"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ) : null}
      </button>
      <div
        className={clsx(
          "text-body1 empty:hidden",
          isDarkMode ? "text-white/80" : "text-black-50"
        )}
        {...api?.getItemContentProps({ value: id || defaultId })}
      >
        {children}
      </div>
    </div>
  );
}
