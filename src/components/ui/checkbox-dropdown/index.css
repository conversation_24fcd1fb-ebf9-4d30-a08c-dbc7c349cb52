.custom-select {
  position: relative;
  width: fit-content;
  box-sizing: border-box;
}

.select-box {
  border-radius: 8px;
  padding: 6px 10px;
  cursor: pointer;
  background-color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.select-box.open {
  border: 1px solid #0000001a;
  background-color: #000000cc;
  color: white;
  outline: none;
}

.select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: white;
  max-height: 200px;
  overflow-y: auto;
  margin-top: 8px;
  padding: 16px;
  border-radius: 12px;
  box-shadow: 0px 4px 25px 0px var(--color-black-5);
  transform-origin: top center;
  animation: zoomIn 0.1s ease-out forwards;
}

.select-dropdown.closing {
  animation: zoomOut 0.08s ease-in forwards;
}

@keyframes zoomIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes zoomOut {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateY(-10px);
  }
}

.select-option {
  cursor: pointer;
}

.select-option:hover {
  background-color: #f0f0f0;
}

.select-option.selected {
  background-color: #e6f4ff;
  font-weight: bold;
}

.arrow {
  font-size: 10px;
}
