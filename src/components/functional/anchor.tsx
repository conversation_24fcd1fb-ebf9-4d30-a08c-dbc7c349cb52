import type { AnchorHTMLAttributes, Ref, MouseEvent } from "react";
import { Link } from "react-router";
import useNavigate from "@/hooks/navigate";

type AnchorProps = AnchorHTMLAttributes<HTMLAnchorElement> & {
  ref?: Ref<HTMLAnchorElement>;
  replace?: boolean;
};

export default function Anchor({
  children,
  href,
  onClick,
  replace,
  ...props
}: AnchorProps) {
  const navigate = useNavigate();

  // preload function available but not called (was commented out in Svelte version)
  // function preload() {
  //   const chunks = href?.split("/") ?? [];
  //   if (href?.startsWith("/bonds/") && chunks.length === 4) {
  //     const bondId = chunks[3];
  //     prefetchBondDetails(queryClient, bondId);
  //     prefetchPersonalizationPage(queryClient, "bond-details", {
  //       bond_id: bondId,
  //     });
  //   }
  //   if (href?.startsWith("/dynamic/")) {
  //     const params = new URL(href, location.origin).searchParams.get("params");
  //     const path = chunks[3];
  //     if (path) {
  //       prefetchPersonalizationPage(
  //         queryClient,
  //         path,
  //         params ? JSON.parse(params) : undefined,
  //       );
  //     }
  //   }
  // }

  function handleClick(event: MouseEvent<HTMLAnchorElement>) {
    event.preventDefault();
    if (href) navigate(href, { replace });
    onClick?.(event);
  }

  const destination = new URL(href!, window.location.origin);
  if (destination.host !== window.location.host) {
    return (
      <a
        href={href!}
        target="_blank"
        rel="noopener noreferrer"
        {...props}
        onClick={onClick}
      >
        {children}
      </a>
    );
  }

  return (
    <Link to={href!} {...props} onClick={handleClick}>
      {children}
    </Link>
  );
}
